const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Driver = require('../models/Driver');

const JWT_SECRET = process.env.JWT_SECRET || 'baroride_secret_key';

// General authentication middleware
const auth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ message: 'Access denied. No token provided.' });
    }

    const decoded = jwt.verify(token, JWT_SECRET);
    
    let user;
    if (decoded.type === 'user') {
      user = await User.findById(decoded.id);
    } else if (decoded.type === 'driver') {
      user = await Driver.findById(decoded.id);
    }

    if (!user) {
      return res.status(401).json({ message: 'Invalid token.' });
    }

    req.user = user;
    req.userType = decoded.type;
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(401).json({ message: 'Invalid token.' });
  }
};

// User-only authentication middleware
const userAuth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ message: 'Access denied. No token provided.' });
    }

    const decoded = jwt.verify(token, JWT_SECRET);
    
    if (decoded.type !== 'user') {
      return res.status(403).json({ message: 'Access denied. User access required.' });
    }

    const user = await User.findById(decoded.id);
    if (!user) {
      return res.status(401).json({ message: 'Invalid token.' });
    }

    req.user = user;
    req.userType = 'user';
    next();
  } catch (error) {
    console.error('User auth middleware error:', error);
    res.status(401).json({ message: 'Invalid token.' });
  }
};

// Driver-only authentication middleware
const driverAuth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ message: 'Access denied. No token provided.' });
    }

    const decoded = jwt.verify(token, JWT_SECRET);
    
    if (decoded.type !== 'driver') {
      return res.status(403).json({ message: 'Access denied. Driver access required.' });
    }

    const driver = await Driver.findById(decoded.id);
    if (!driver) {
      return res.status(401).json({ message: 'Invalid token.' });
    }

    req.user = driver;
    req.userType = 'driver';
    next();
  } catch (error) {
    console.error('Driver auth middleware error:', error);
    res.status(401).json({ message: 'Invalid token.' });
  }
};

module.exports = { auth, userAuth, driverAuth };
