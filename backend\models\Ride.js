const mongoose = require('mongoose');

const rideSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  driver: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Driver',
    default: null
  },
  pickupLocation: {
    address: {
      type: String,
      required: true
    },
    latitude: {
      type: Number,
      required: true
    },
    longitude: {
      type: Number,
      required: true
    }
  },
  destination: {
    address: {
      type: String,
      default: 'Gambella Airport, Gambella, Ethiopia'
    },
    latitude: {
      type: Number,
      default: 8.1281 // Gambella Airport coordinates
    },
    longitude: {
      type: Number,
      default: 34.5631 // Gambella Airport coordinates
    }
  },
  vehicleType: {
    type: String,
    enum: ['sedan', 'suv', 'van', 'pickup'],
    required: true
  },
  passengerCount: {
    type: Number,
    required: true,
    min: 1,
    max: 8
  },
  status: {
    type: String,
    enum: [
      'requested',
      'driver_assigned',
      'driver_en_route',
      'driver_arrived',
      'in_progress',
      'completed',
      'cancelled'
    ],
    default: 'requested'
  },
  scheduledTime: {
    type: Date,
    required: true
  },
  estimatedDuration: {
    type: Number, // in minutes
    default: null
  },
  estimatedDistance: {
    type: Number, // in kilometers
    default: null
  },
  actualStartTime: {
    type: Date,
    default: null
  },
  actualEndTime: {
    type: Date,
    default: null
  },
  fare: {
    type: Number,
    default: 0 // Free service
  },
  rating: {
    userRating: {
      type: Number,
      min: 1,
      max: 5,
      default: null
    },
    driverRating: {
      type: Number,
      min: 1,
      max: 5,
      default: null
    },
    userComment: {
      type: String,
      default: null
    },
    driverComment: {
      type: String,
      default: null
    }
  },
  specialRequests: {
    type: String,
    default: null
  },
  cancellationReason: {
    type: String,
    default: null
  },
  cancelledBy: {
    type: String,
    enum: ['user', 'driver', 'system'],
    default: null
  },
  trackingData: [{
    latitude: Number,
    longitude: Number,
    timestamp: {
      type: Date,
      default: Date.now
    },
    speed: Number,
    heading: Number
  }]
}, {
  timestamps: true
});

// Index for efficient querying
rideSchema.index({ user: 1, createdAt: -1 });
rideSchema.index({ driver: 1, createdAt: -1 });
rideSchema.index({ status: 1, scheduledTime: 1 });
rideSchema.index({ 'pickupLocation.latitude': 1, 'pickupLocation.longitude': 1 });

// Virtual for ride duration
rideSchema.virtual('actualDuration').get(function() {
  if (this.actualStartTime && this.actualEndTime) {
    return Math.round((this.actualEndTime - this.actualStartTime) / (1000 * 60)); // in minutes
  }
  return null;
});

// Method to update ride status
rideSchema.methods.updateStatus = function(newStatus, additionalData = {}) {
  this.status = newStatus;
  
  if (newStatus === 'in_progress' && !this.actualStartTime) {
    this.actualStartTime = new Date();
  }
  
  if (newStatus === 'completed' && !this.actualEndTime) {
    this.actualEndTime = new Date();
  }
  
  Object.assign(this, additionalData);
  return this.save();
};

// Method to add tracking point
rideSchema.methods.addTrackingPoint = function(latitude, longitude, speed = null, heading = null) {
  this.trackingData.push({
    latitude,
    longitude,
    speed,
    heading,
    timestamp: new Date()
  });
  return this.save();
};

// Static method to find available rides for drivers
rideSchema.statics.findAvailableRides = function(driverLocation, vehicleType, maxDistance = 10) {
  return this.find({
    status: 'requested',
    vehicleType: vehicleType,
    scheduledTime: { $gte: new Date() }
  }).populate('user', 'name phone');
};

module.exports = mongoose.model('Ride', rideSchema);
