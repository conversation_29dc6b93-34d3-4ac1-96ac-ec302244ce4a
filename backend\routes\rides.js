const express = require('express');
const geolib = require('geolib');
const Ride = require('../models/Ride');
const Driver = require('../models/Driver');
const User = require('../models/User');
const { userAuth, driverAuth, auth } = require('../middleware/auth');

const router = express.Router();

// Create a new ride request
router.post('/request', userAuth, async (req, res) => {
  try {
    const {
      pickupLocation,
      vehicleType,
      passengerCount,
      scheduledTime,
      specialRequests
    } = req.body;

    // Validate required fields
    if (!pickupLocation || !vehicleType || !passengerCount || !scheduledTime) {
      return res.status(400).json({
        message: 'Missing required fields: pickupLocation, vehicleType, passengerCount, scheduledTime'
      });
    }

    // Create new ride
    const ride = new Ride({
      user: req.user._id,
      pickupLocation,
      vehicleType,
      passengerCount,
      scheduledTime: new Date(scheduledTime),
      specialRequests
    });

    await ride.save();
    await ride.populate('user', 'name phone');

    // Find available drivers for matching
    const availableDrivers = await findNearbyDrivers(
      pickupLocation.latitude,
      pickupLocation.longitude,
      vehicleType,
      passengerCount
    );

    // Emit ride request to available drivers via Socket.IO
    const { io } = require('../server');
    availableDrivers.forEach(driver => {
      io.to(`driver_${driver._id}`).emit('new-ride-request', {
        rideId: ride._id,
        pickup: pickupLocation,
        vehicleType,
        passengerCount,
        scheduledTime,
        user: {
          name: ride.user.name,
          phone: ride.user.phone
        }
      });
    });

    res.status(201).json({
      message: 'Ride requested successfully',
      ride: {
        id: ride._id,
        status: ride.status,
        pickupLocation: ride.pickupLocation,
        destination: ride.destination,
        vehicleType: ride.vehicleType,
        passengerCount: ride.passengerCount,
        scheduledTime: ride.scheduledTime,
        availableDrivers: availableDrivers.length
      }
    });
  } catch (error) {
    console.error('Ride request error:', error);
    res.status(500).json({ message: 'Server error while creating ride request' });
  }
});

// Get user's rides
router.get('/user/rides', userAuth, async (req, res) => {
  try {
    const { status, limit = 10, page = 1 } = req.query;
    
    const query = { user: req.user._id };
    if (status) {
      query.status = status;
    }

    const rides = await Ride.find(query)
      .populate('driver', 'name phone vehicle rating')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit));

    res.json({
      rides,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: await Ride.countDocuments(query)
      }
    });
  } catch (error) {
    console.error('Get user rides error:', error);
    res.status(500).json({ message: 'Server error while fetching rides' });
  }
});

// Get driver's available rides
router.get('/driver/available', driverAuth, async (req, res) => {
  try {
    const driver = req.user;
    
    if (!driver.currentLocation.latitude || !driver.currentLocation.longitude) {
      return res.status(400).json({
        message: 'Driver location not available. Please update your location.'
      });
    }

    const availableRides = await Ride.find({
      status: 'requested',
      vehicleType: driver.vehicle.type,
      passengerCount: { $lte: driver.vehicle.capacity },
      scheduledTime: { $gte: new Date() }
    }).populate('user', 'name phone');

    // Calculate distance for each ride
    const ridesWithDistance = availableRides.map(ride => {
      const distance = geolib.getDistance(
        {
          latitude: driver.currentLocation.latitude,
          longitude: driver.currentLocation.longitude
        },
        {
          latitude: ride.pickupLocation.latitude,
          longitude: ride.pickupLocation.longitude
        }
      ) / 1000; // Convert to kilometers

      return {
        ...ride.toObject(),
        distance: Math.round(distance * 10) / 10 // Round to 1 decimal place
      };
    });

    // Sort by distance
    ridesWithDistance.sort((a, b) => a.distance - b.distance);

    res.json({
      rides: ridesWithDistance.slice(0, 20) // Limit to 20 nearest rides
    });
  } catch (error) {
    console.error('Get available rides error:', error);
    res.status(500).json({ message: 'Server error while fetching available rides' });
  }
});

// Driver accepts a ride
router.post('/:rideId/accept', driverAuth, async (req, res) => {
  try {
    const { rideId } = req.params;
    const driver = req.user;

    // Check if driver is available
    if (!driver.isAvailable || driver.currentRide) {
      return res.status(400).json({
        message: 'Driver is not available for new rides'
      });
    }

    const ride = await Ride.findById(rideId).populate('user', 'name phone');
    
    if (!ride) {
      return res.status(404).json({ message: 'Ride not found' });
    }

    if (ride.status !== 'requested') {
      return res.status(400).json({
        message: 'Ride is no longer available'
      });
    }

    // Update ride and driver
    ride.driver = driver._id;
    ride.status = 'driver_assigned';
    await ride.save();

    driver.currentRide = ride._id;
    driver.isAvailable = false;
    await driver.save();

    // Notify user via Socket.IO
    const { io } = require('../server');
    io.to(`user_${ride.user._id}`).emit('driver-assigned', {
      rideId: ride._id,
      driver: {
        id: driver._id,
        name: driver.name,
        phone: driver.phone,
        vehicle: driver.vehicle,
        rating: driver.rating,
        currentLocation: driver.currentLocation
      }
    });

    res.json({
      message: 'Ride accepted successfully',
      ride: {
        id: ride._id,
        status: ride.status,
        user: ride.user,
        pickupLocation: ride.pickupLocation,
        destination: ride.destination,
        scheduledTime: ride.scheduledTime
      }
    });
  } catch (error) {
    console.error('Accept ride error:', error);
    res.status(500).json({ message: 'Server error while accepting ride' });
  }
});

// Update ride status
router.patch('/:rideId/status', auth, async (req, res) => {
  try {
    const { rideId } = req.params;
    const { status, message } = req.body;

    const ride = await Ride.findById(rideId)
      .populate('user', 'name phone')
      .populate('driver', 'name phone vehicle');

    if (!ride) {
      return res.status(404).json({ message: 'Ride not found' });
    }

    // Check authorization
    const isAuthorized = 
      (req.userType === 'user' && ride.user._id.toString() === req.user._id.toString()) ||
      (req.userType === 'driver' && ride.driver && ride.driver._id.toString() === req.user._id.toString());

    if (!isAuthorized) {
      return res.status(403).json({ message: 'Not authorized to update this ride' });
    }

    // Update ride status
    await ride.updateStatus(status);

    // Update driver availability if ride is completed or cancelled
    if (status === 'completed' || status === 'cancelled') {
      if (ride.driver) {
        await Driver.findByIdAndUpdate(ride.driver._id, {
          isAvailable: true,
          currentRide: null
        });
      }
    }

    // Notify other party via Socket.IO
    const { io } = require('../server');
    const targetUserId = req.userType === 'user' ? 
      `driver_${ride.driver._id}` : 
      `user_${ride.user._id}`;

    io.to(targetUserId).emit('ride-status-update', {
      rideId: ride._id,
      status,
      message,
      timestamp: new Date()
    });

    res.json({
      message: 'Ride status updated successfully',
      ride: {
        id: ride._id,
        status: ride.status,
        actualStartTime: ride.actualStartTime,
        actualEndTime: ride.actualEndTime
      }
    });
  } catch (error) {
    console.error('Update ride status error:', error);
    res.status(500).json({ message: 'Server error while updating ride status' });
  }
});

// Add tracking point to ride
router.post('/:rideId/tracking', driverAuth, async (req, res) => {
  try {
    const { rideId } = req.params;
    const { latitude, longitude, speed, heading } = req.body;

    const ride = await Ride.findById(rideId);
    
    if (!ride) {
      return res.status(404).json({ message: 'Ride not found' });
    }

    if (ride.driver.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Not authorized to update tracking for this ride' });
    }

    // Add tracking point
    await ride.addTrackingPoint(latitude, longitude, speed, heading);

    // Update driver's current location
    await req.user.updateLocation(latitude, longitude);

    // Emit location update via Socket.IO
    const { io } = require('../server');
    io.to(`user_${ride.user}`).emit('driver-location-update', {
      rideId: ride._id,
      latitude,
      longitude,
      speed,
      heading,
      timestamp: new Date()
    });

    res.json({
      message: 'Location updated successfully',
      location: { latitude, longitude, speed, heading }
    });
  } catch (error) {
    console.error('Add tracking point error:', error);
    res.status(500).json({ message: 'Server error while updating location' });
  }
});

// Get ride details
router.get('/:rideId', auth, async (req, res) => {
  try {
    const { rideId } = req.params;

    const ride = await Ride.findById(rideId)
      .populate('user', 'name phone')
      .populate('driver', 'name phone vehicle rating currentLocation');

    if (!ride) {
      return res.status(404).json({ message: 'Ride not found' });
    }

    // Check authorization
    const isAuthorized = 
      (req.userType === 'user' && ride.user._id.toString() === req.user._id.toString()) ||
      (req.userType === 'driver' && ride.driver && ride.driver._id.toString() === req.user._id.toString());

    if (!isAuthorized) {
      return res.status(403).json({ message: 'Not authorized to view this ride' });
    }

    res.json({ ride });
  } catch (error) {
    console.error('Get ride details error:', error);
    res.status(500).json({ message: 'Server error while fetching ride details' });
  }
});

// Helper function to find nearby drivers
async function findNearbyDrivers(latitude, longitude, vehicleType, passengerCount, maxDistance = 10000) {
  try {
    const drivers = await Driver.find({
      isOnline: true,
      isAvailable: true,
      isVerified: true,
      'vehicle.type': vehicleType,
      'vehicle.capacity': { $gte: passengerCount },
      'currentLocation.latitude': { $exists: true },
      'currentLocation.longitude': { $exists: true }
    });

    // Filter drivers by distance
    const nearbyDrivers = drivers.filter(driver => {
      const distance = geolib.getDistance(
        { latitude, longitude },
        {
          latitude: driver.currentLocation.latitude,
          longitude: driver.currentLocation.longitude
        }
      );
      return distance <= maxDistance;
    });

    // Sort by distance
    nearbyDrivers.sort((a, b) => {
      const distanceA = geolib.getDistance(
        { latitude, longitude },
        {
          latitude: a.currentLocation.latitude,
          longitude: a.currentLocation.longitude
        }
      );
      const distanceB = geolib.getDistance(
        { latitude, longitude },
        {
          latitude: b.currentLocation.latitude,
          longitude: b.currentLocation.longitude
        }
      );
      return distanceA - distanceB;
    });

    return nearbyDrivers;
  } catch (error) {
    console.error('Find nearby drivers error:', error);
    return [];
  }
}

module.exports = router;
