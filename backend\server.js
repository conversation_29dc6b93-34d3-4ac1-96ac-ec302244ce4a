const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const http = require('http');
const socketIo = require('socket.io');
require('dotenv').config();

const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const rideRoutes = require('./routes/rides');
const driverRoutes = require('./routes/drivers');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());

// MongoDB connection
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/baroride', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => console.log('Connected to MongoDB'))
.catch(err => console.error('MongoDB connection error:', err));

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/rides', rideRoutes);
app.use('/api/drivers', driverRoutes);

// Socket.io for real-time tracking
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  // Join ride room for real-time updates
  socket.on('join-ride', (rideId) => {
    socket.join(rideId);
    console.log(`User ${socket.id} joined ride ${rideId}`);
  });

  // Driver location updates
  socket.on('driver-location-update', (data) => {
    socket.to(data.rideId).emit('driver-location', {
      latitude: data.latitude,
      longitude: data.longitude,
      timestamp: new Date()
    });
  });

  // Ride status updates
  socket.on('ride-status-update', (data) => {
    socket.to(data.rideId).emit('ride-status', {
      status: data.status,
      message: data.message,
      timestamp: new Date()
    });
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'BaroRide API is running' });
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`BaroRide server running on port ${PORT}`);
});

module.exports = { app, io };
