const express = require('express');
const Driver = require('../models/Driver');
const { driverAuth } = require('../middleware/auth');

const router = express.Router();

// Get driver profile
router.get('/profile', driverAuth, async (req, res) => {
  try {
    const driver = await Driver.findById(req.user._id)
      .populate('rideHistory', 'status createdAt pickupLocation user')
      .select('-password');

    if (!driver) {
      return res.status(404).json({ message: 'Driver not found' });
    }

    res.json({ driver });
  } catch (error) {
    console.error('Get driver profile error:', error);
    res.status(500).json({ message: 'Server error while fetching profile' });
  }
});

// Update driver profile
router.put('/profile', driverAuth, async (req, res) => {
  try {
    const { name, phone, vehicle, profilePicture } = req.body;

    const updateData = {};
    if (name) updateData.name = name;
    if (phone) updateData.phone = phone;
    if (vehicle) updateData.vehicle = vehicle;
    if (profilePicture) updateData.profilePicture = profilePicture;

    const driver = await Driver.findByIdAndUpdate(
      req.user._id,
      updateData,
      { new: true, runValidators: true }
    ).select('-password');

    if (!driver) {
      return res.status(404).json({ message: 'Driver not found' });
    }

    res.json({
      message: 'Profile updated successfully',
      driver
    });
  } catch (error) {
    console.error('Update driver profile error:', error);
    if (error.code === 11000) {
      return res.status(400).json({ message: 'Phone number or plate number already exists' });
    }
    res.status(500).json({ message: 'Server error while updating profile' });
  }
});

// Update driver location
router.put('/location', driverAuth, async (req, res) => {
  try {
    const { latitude, longitude } = req.body;

    if (!latitude || !longitude) {
      return res.status(400).json({
        message: 'Latitude and longitude are required'
      });
    }

    await req.user.updateLocation(latitude, longitude);

    res.json({
      message: 'Location updated successfully',
      location: {
        latitude,
        longitude,
        lastUpdated: new Date()
      }
    });
  } catch (error) {
    console.error('Update driver location error:', error);
    res.status(500).json({ message: 'Server error while updating location' });
  }
});

// Update driver online status
router.put('/status', driverAuth, async (req, res) => {
  try {
    const { isOnline, isAvailable } = req.body;

    const updateData = {};
    if (typeof isOnline === 'boolean') updateData.isOnline = isOnline;
    if (typeof isAvailable === 'boolean') updateData.isAvailable = isAvailable;

    // If going offline, also set unavailable
    if (isOnline === false) {
      updateData.isAvailable = false;
    }

    const driver = await Driver.findByIdAndUpdate(
      req.user._id,
      updateData,
      { new: true }
    ).select('-password');

    res.json({
      message: 'Status updated successfully',
      status: {
        isOnline: driver.isOnline,
        isAvailable: driver.isAvailable
      }
    });
  } catch (error) {
    console.error('Update driver status error:', error);
    res.status(500).json({ message: 'Server error while updating status' });
  }
});

// Get driver statistics
router.get('/stats', driverAuth, async (req, res) => {
  try {
    const driverId = req.user._id;

    const stats = await Driver.aggregate([
      { $match: { _id: driverId } },
      {
        $lookup: {
          from: 'rides',
          localField: '_id',
          foreignField: 'driver',
          as: 'rides'
        }
      },
      {
        $project: {
          totalRides: { $size: '$rides' },
          completedRides: {
            $size: {
              $filter: {
                input: '$rides',
                cond: { $eq: ['$$this.status', 'completed'] }
              }
            }
          },
          cancelledRides: {
            $size: {
              $filter: {
                input: '$rides',
                cond: { $eq: ['$$this.status', 'cancelled'] }
              }
            }
          },
          averageRating: '$rating.average',
          ratingCount: '$rating.count',
          totalDistance: {
            $sum: {
              $map: {
                input: {
                  $filter: {
                    input: '$rides',
                    cond: { $ne: ['$$this.estimatedDistance', null] }
                  }
                },
                in: '$$this.estimatedDistance'
              }
            }
          },
          totalDuration: {
            $sum: {
              $map: {
                input: {
                  $filter: {
                    input: '$rides',
                    cond: { 
                      $and: [
                        { $ne: ['$$this.actualStartTime', null] },
                        { $ne: ['$$this.actualEndTime', null] }
                      ]
                    }
                  }
                },
                in: {
                  $divide: [
                    { $subtract: ['$$this.actualEndTime', '$$this.actualStartTime'] },
                    60000 // Convert to minutes
                  ]
                }
              }
            }
          }
        }
      }
    ]);

    const driverStats = stats[0] || {
      totalRides: 0,
      completedRides: 0,
      cancelledRides: 0,
      averageRating: 5.0,
      ratingCount: 0,
      totalDistance: 0,
      totalDuration: 0
    };

    res.json({ stats: driverStats });
  } catch (error) {
    console.error('Get driver stats error:', error);
    res.status(500).json({ message: 'Server error while fetching statistics' });
  }
});

// Get driver's current ride
router.get('/current-ride', driverAuth, async (req, res) => {
  try {
    const driver = req.user;

    if (!driver.currentRide) {
      return res.json({ currentRide: null });
    }

    const currentRide = await driver.populate({
      path: 'currentRide',
      populate: {
        path: 'user',
        select: 'name phone'
      }
    });

    res.json({ currentRide: currentRide.currentRide });
  } catch (error) {
    console.error('Get current ride error:', error);
    res.status(500).json({ message: 'Server error while fetching current ride' });
  }
});

// Get driver's ride history
router.get('/rides', driverAuth, async (req, res) => {
  try {
    const { status, limit = 10, page = 1 } = req.query;
    
    const query = { driver: req.user._id };
    if (status) {
      query.status = status;
    }

    const Ride = require('../models/Ride');
    const rides = await Ride.find(query)
      .populate('user', 'name phone')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit));

    res.json({
      rides,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: await Ride.countDocuments(query)
      }
    });
  } catch (error) {
    console.error('Get driver rides error:', error);
    res.status(500).json({ message: 'Server error while fetching rides' });
  }
});

// Get nearby drivers (for admin/testing purposes)
router.get('/nearby', async (req, res) => {
  try {
    const { latitude, longitude, radius = 10 } = req.query;

    if (!latitude || !longitude) {
      return res.status(400).json({
        message: 'Latitude and longitude are required'
      });
    }

    const drivers = await Driver.find({
      isOnline: true,
      isAvailable: true,
      'currentLocation.latitude': { $exists: true },
      'currentLocation.longitude': { $exists: true }
    }).select('name vehicle currentLocation rating');

    // Filter by distance (simple implementation)
    const geolib = require('geolib');
    const nearbyDrivers = drivers.filter(driver => {
      const distance = geolib.getDistance(
        { latitude: parseFloat(latitude), longitude: parseFloat(longitude) },
        {
          latitude: driver.currentLocation.latitude,
          longitude: driver.currentLocation.longitude
        }
      );
      return distance <= radius * 1000; // Convert km to meters
    });

    res.json({ drivers: nearbyDrivers });
  } catch (error) {
    console.error('Get nearby drivers error:', error);
    res.status(500).json({ message: 'Server error while fetching nearby drivers' });
  }
});

// Delete driver account
router.delete('/account', driverAuth, async (req, res) => {
  try {
    const { password } = req.body;

    if (!password) {
      return res.status(400).json({ message: 'Password is required to delete account' });
    }

    const driver = await Driver.findById(req.user._id);
    const isMatch = await driver.comparePassword(password);

    if (!isMatch) {
      return res.status(401).json({ message: 'Invalid password' });
    }

    // Check if driver has active rides
    if (driver.currentRide) {
      return res.status(400).json({
        message: 'Cannot delete account while having an active ride'
      });
    }

    // Soft delete - deactivate account
    await Driver.findByIdAndUpdate(req.user._id, { 
      isActive: false,
      isOnline: false,
      isAvailable: false
    });

    res.json({ message: 'Account deactivated successfully' });
  } catch (error) {
    console.error('Delete driver account error:', error);
    res.status(500).json({ message: 'Server error while deleting account' });
  }
});

module.exports = router;
