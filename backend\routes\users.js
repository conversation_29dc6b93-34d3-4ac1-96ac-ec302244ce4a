const express = require('express');
const User = require('../models/User');
const { userAuth } = require('../middleware/auth');

const router = express.Router();

// Get user profile
router.get('/profile', userAuth, async (req, res) => {
  try {
    const user = await User.findById(req.user._id)
      .populate('rideHistory', 'status createdAt destination driver')
      .select('-password');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({ user });
  } catch (error) {
    console.error('Get user profile error:', error);
    res.status(500).json({ message: 'Server error while fetching profile' });
  }
});

// Update user profile
router.put('/profile', userAuth, async (req, res) => {
  try {
    const { name, phone, homeAddress, profilePicture } = req.body;

    const updateData = {};
    if (name) updateData.name = name;
    if (phone) updateData.phone = phone;
    if (homeAddress) updateData.homeAddress = homeAddress;
    if (profilePicture) updateData.profilePicture = profilePicture;

    const user = await User.findByIdAndUpdate(
      req.user._id,
      updateData,
      { new: true, runValidators: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({
      message: 'Profile updated successfully',
      user
    });
  } catch (error) {
    console.error('Update user profile error:', error);
    if (error.code === 11000) {
      return res.status(400).json({ message: 'Phone number already exists' });
    }
    res.status(500).json({ message: 'Server error while updating profile' });
  }
});

// Update home address
router.put('/home-address', userAuth, async (req, res) => {
  try {
    const { address, latitude, longitude } = req.body;

    if (!address || !latitude || !longitude) {
      return res.status(400).json({
        message: 'Address, latitude, and longitude are required'
      });
    }

    const user = await User.findByIdAndUpdate(
      req.user._id,
      {
        homeAddress: {
          address,
          latitude,
          longitude
        }
      },
      { new: true }
    ).select('-password');

    res.json({
      message: 'Home address updated successfully',
      homeAddress: user.homeAddress
    });
  } catch (error) {
    console.error('Update home address error:', error);
    res.status(500).json({ message: 'Server error while updating home address' });
  }
});

// Get user statistics
router.get('/stats', userAuth, async (req, res) => {
  try {
    const userId = req.user._id;

    const stats = await User.aggregate([
      { $match: { _id: userId } },
      {
        $lookup: {
          from: 'rides',
          localField: '_id',
          foreignField: 'user',
          as: 'rides'
        }
      },
      {
        $project: {
          totalRides: { $size: '$rides' },
          completedRides: {
            $size: {
              $filter: {
                input: '$rides',
                cond: { $eq: ['$$this.status', 'completed'] }
              }
            }
          },
          cancelledRides: {
            $size: {
              $filter: {
                input: '$rides',
                cond: { $eq: ['$$this.status', 'cancelled'] }
              }
            }
          },
          averageRating: {
            $avg: {
              $map: {
                input: {
                  $filter: {
                    input: '$rides',
                    cond: { $ne: ['$$this.rating.userRating', null] }
                  }
                },
                in: '$$this.rating.userRating'
              }
            }
          }
        }
      }
    ]);

    const userStats = stats[0] || {
      totalRides: 0,
      completedRides: 0,
      cancelledRides: 0,
      averageRating: null
    };

    res.json({ stats: userStats });
  } catch (error) {
    console.error('Get user stats error:', error);
    res.status(500).json({ message: 'Server error while fetching statistics' });
  }
});

// Delete user account
router.delete('/account', userAuth, async (req, res) => {
  try {
    const { password } = req.body;

    if (!password) {
      return res.status(400).json({ message: 'Password is required to delete account' });
    }

    const user = await User.findById(req.user._id);
    const isMatch = await user.comparePassword(password);

    if (!isMatch) {
      return res.status(401).json({ message: 'Invalid password' });
    }

    // Soft delete - deactivate account
    await User.findByIdAndUpdate(req.user._id, { isActive: false });

    res.json({ message: 'Account deactivated successfully' });
  } catch (error) {
    console.error('Delete user account error:', error);
    res.status(500).json({ message: 'Server error while deleting account' });
  }
});

module.exports = router;
