{"name": "baroride", "version": "1.0.0", "description": "BaroRide - Free ride service to Gambella Airport", "main": "index.js", "scripts": {"start": "node backend/server.js", "dev": "nodemon backend/server.js", "mobile": "cd mobile && npm start", "install-all": "npm install && cd mobile && npm install"}, "keywords": ["ride-sharing", "transportation", "react-native", "nodejs"], "author": "BaroRide Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "socket.io": "^4.7.2", "uuid": "^9.0.0", "geolib": "^3.3.4"}, "devDependencies": {"nodemon": "^3.0.1"}}